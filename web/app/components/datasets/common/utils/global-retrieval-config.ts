import type { RetrievalConfig } from '@/types/datasets'

// 全局检索配置的存储键
const GLOBAL_RETRIEVAL_CONFIG_KEY = 'global_retrieval_config'

// 扩展的全局检索配置类型，包含元数据
export type GlobalRetrievalConfig = {
  timestamp: number
  version: string
  source: 'user_setting' | 'default' | 'imported'
} & RetrievalConfig

/**
 * 保存检索配置到全局状态
 * @param config 检索配置
 * @param source 配置来源
 */
export const saveGlobalRetrievalConfig = (
  config: RetrievalConfig,
  source: GlobalRetrievalConfig['source'] = 'user_setting',
): boolean => {
  try {
    const globalConfig: GlobalRetrievalConfig = {
      ...config,
      timestamp: Date.now(),
      version: '1.0.0',
      source,
    }

    localStorage.setItem(GLOBAL_RETRIEVAL_CONFIG_KEY, JSON.stringify(globalConfig))
    console.log('检索方法配置已保存到全局状态:', globalConfig)

    // 触发自定义事件，通知其他组件配置已更新
    window.dispatchEvent(new CustomEvent('globalRetrievalConfigChanged', {
      detail: globalConfig,
    }))

    return true
  }
  catch (error) {
    console.error('保存检索方法配置到全局状态失败:', error)
    return false
  }
}

/**
 * 从全局状态加载检索配置
 * @returns 检索配置或null
 */
export const loadGlobalRetrievalConfig = (): GlobalRetrievalConfig | null => {
  try {
    const savedConfig = localStorage.getItem(GLOBAL_RETRIEVAL_CONFIG_KEY)
    if (savedConfig) {
      const parsedConfig = JSON.parse(savedConfig) as GlobalRetrievalConfig
      console.log('从全局状态加载检索配置:', parsedConfig)
      return parsedConfig
    }
  }
  catch (error) {
    console.error('从全局状态加载检索配置失败:', error)
  }
  return null
}

/**
 * 清除全局检索配置
 */
export const clearGlobalRetrievalConfig = (): boolean => {
  try {
    localStorage.removeItem(GLOBAL_RETRIEVAL_CONFIG_KEY)
    console.log('全局检索配置已清除')

    // 触发自定义事件，通知其他组件配置已清除
    window.dispatchEvent(new CustomEvent('globalRetrievalConfigCleared'))

    return true
  }
  catch (error) {
    console.error('清除全局检索配置失败:', error)
    return false
  }
}

/**
 * 检查全局配置是否存在
 */
export const hasGlobalRetrievalConfig = (): boolean => {
  return localStorage.getItem(GLOBAL_RETRIEVAL_CONFIG_KEY) !== null
}

/**
 * 验证检索配置的有效性
 */
export const validateRetrievalConfig = (config: any): config is RetrievalConfig => {
  return (
    config
    && typeof config === 'object'
    && typeof config.search_method === 'string'
    && typeof config.reranking_enable === 'boolean'
    && typeof config.top_k === 'number'
    && typeof config.score_threshold_enabled === 'boolean'
    && typeof config.score_threshold === 'number'
    && config.reranking_model
    && typeof config.reranking_model.reranking_provider_name === 'string'
    && typeof config.reranking_model.reranking_model_name === 'string'
  )
}
