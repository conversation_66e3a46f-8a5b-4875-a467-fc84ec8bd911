'use client'
import React, {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import style from '../styles/style.module.css'

import {
  saveGlobalRetrievalConfig,
  validateRetrievalConfig,
} from '../utils/global-retrieval-config'
import cn from '@/utils/classnames'
import RetrievalParamConfigXiyan from '@/app/components/datasets/common/retrieval-param-config-xiyan'
import RadioCard from '@/app/components/base/radio-card'
import { useProviderContext } from '@/context/provider-context'
import {
  RETRIEVE_METHOD,
  RerankingModeEnum,
  type RetrievalConfig,
  WeightedScoreEnum,
} from '@/types/datasets'
import { DEFAULT_WEIGHTED_SCORE } from '@/config/dataset'
// import { useModelListAndDefaultModelAndCurrentProviderAndModel } from '@/app/components/account-setting/model-provider-page/hooks'
// import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
import Citation from '@/app/components/base/features/new-feature-panel/citation'
import {
  useFormattingChangedDispatcher,
} from '@/app/components/app/configuration/debug/hooks'
import { useFeaturesStore } from '@/app/components/base/features/hooks'

type Props = {
  disabled?: boolean
  value: RetrievalConfig
  onChange: (value: RetrievalConfig) => void
  isInApp?: boolean // 是否在应用中设置知识库
}

type ConfigRef = {
  resetConfig: (value: RetrievalConfig) => void
}

const RetrievalMethodConfigXiyan = forwardRef<ConfigRef, Props>((props, ref) => {
  const { value: passValue, disabled = false, onChange, isInApp = false } = props
  const { t } = useTranslation()
  const featuresStore = useFeaturesStore()
  const { supportRetrievalMethods } = useProviderContext()
  const [currentRetrievalMethod, setCurrentRetrievalMethod] = useState(
    RETRIEVE_METHOD.hybrid,
  )
  const formattingChangedDispatcher = useFormattingChangedDispatcher()
  // 变更特性
  const handleFeaturesChange = useCallback((flag: any) => {
    console.log('flag', flag)
    if (flag)
      formattingChangedDispatcher()
  }, [formattingChangedDispatcher])
  // const {
  //   modelList: embeddingModelList,
  //   defaultModel: embeddingDefaultModel,
  //   mutateModelList: mutateEmbeddingModelList,
  //   isModelListLoading: isEmbeddingModelListLoading,
  // } = useModelListAndDefaultModelAndCurrentProviderAndModel(ModelTypeEnum.rerank)

  // const semanticRef = useRef<{
  //   getConfig: () => RetrievalConfig
  //   resetConfig: () => void
  // }>()

  const paramsRef = useRef<{
    getConfig: () => RetrievalConfig
    resetConfig: () => void
  }>()

  const hybridRef = useRef<{
    getConfig: () => RetrievalConfig
    resetConfig: () => void
  }>()

  // 数据预处理
  const value = useMemo(() => {
    if (passValue) {
      if (!passValue.weights) {
        return {
          ...passValue,
          weights: {
            weight_type: WeightedScoreEnum.Customized,
            vector_setting: {
              vector_weight: DEFAULT_WEIGHTED_SCORE.other.semantic,
              embedding_provider_name: '',
              embedding_model_name: '',
            },
            keyword_setting: {
              keyword_weight: DEFAULT_WEIGHTED_SCORE.other.keyword,
            },
          },
        }
      }
      return passValue
    }
    return undefined
  }, [passValue])

  // 变更索引方法
  const changeRetrievalMethod = (retrivalMethod: RETRIEVE_METHOD) => {
    setCurrentRetrievalMethod(retrivalMethod)
    const config = {
      ...value,
      search_method: retrivalMethod,
    } as RetrievalConfig

    // 1. 组件层面的状态更新 - 通知父组件
    onChange(config)

    // 2. 功能特性存储更新 - 用于特性面板
    if (featuresStore) {
      const { setFeatures } = featuresStore.getState()
      setFeatures(config as any)
    }

    // 3. 全局状态持久化存储 - 主要存储方法
    if (validateRetrievalConfig(config))
      saveGlobalRetrievalConfig(config, 'user_setting')
    else
      console.warn('检索配置验证失败，未保存到全局状态:', config)
  }

  useEffect(() => {
    if (value)
      setCurrentRetrievalMethod(value.search_method)
  }, [value])

  useEffect(() => {
    if (!passValue && supportRetrievalMethods.length > 0) {
      const defaultMethod = supportRetrievalMethods[0]
      setCurrentRetrievalMethod(defaultMethod)
      const config: RetrievalConfig = {
        search_method: defaultMethod,
        reranking_enable: true,
        reranking_mode: RerankingModeEnum.WeightedScore,
        reranking_model: {
          reranking_provider_name: 'tong_yi',
          reranking_model_name: 'gte-rerank',
        },
        weights: {
          weight_type: WeightedScoreEnum.Customized,
          keyword_setting: {
            keyword_weight: 0.3,
          },
          vector_setting: {
            vector_weight: 0.7,
            embedding_provider_name: '',
            embedding_model_name: '',
          },
        },
        top_k: 3,
        score_threshold_enabled: true,
        score_threshold: 0.5,
      }
      onChange(config)
    }
  }, [])

  useImperativeHandle(ref, () => ({
    resetConfig: (config: RetrievalConfig) => {
      setCurrentRetrievalMethod(config.search_method)
      onChange(config)
    },
  }))

  const isHybrid = currentRetrievalMethod === RETRIEVE_METHOD.hybrid
  const isSemantic = currentRetrievalMethod === RETRIEVE_METHOD.semantic
  const isFulltext = currentRetrievalMethod === RETRIEVE_METHOD.fullText

  return (
    <div className={style.wrap}>
      {/* 检索方式选择 */}
      <div className="space-y-2">
        {supportRetrievalMethods.includes(RETRIEVE_METHOD.hybrid) && (
          <RadioCard
            disabled={disabled}
            className={cn(style.radioItem, isHybrid && 'active')}
            title={t('dataset.retrieval.hybrid_search.title')}
            description={t('dataset.retrieval.hybrid_search.description')}
            isChosen={isHybrid}
            onChosen={() => changeRetrievalMethod(RETRIEVE_METHOD.hybrid)}
            chosenConfig={
              <div className="space-y-4">
                {/* 权重配置 */}
                <RetrievalParamConfigXiyan
                  ref={hybridRef}
                  type={RETRIEVE_METHOD.hybrid}
                  value={value}
                  onChange={onChange}
                  showConfig={false}
                  showTopKItem={false}
                />
              </div>
            }
          />
        )}
        {supportRetrievalMethods.includes(RETRIEVE_METHOD.fullText) && (
          <RadioCard
            disabled={disabled}
            className={cn(style.radioItem, isFulltext && 'active')}
            title={t('dataset.retrieval.full_text_search.title')}
            description={t('dataset.retrieval.full_text_search.description')}
            isChosen={isFulltext}
            onChosen={() => changeRetrievalMethod(RETRIEVE_METHOD.fullText)}
          />
        )}
        {supportRetrievalMethods.includes(RETRIEVE_METHOD.semantic) && (
          <RadioCard
            disabled={disabled}
            className={cn(style.radioItem, isSemantic && 'active')}
            title={t('dataset.retrieval.semantic_search.title')}
            description={t('dataset.retrieval.semantic_search.description')}
            isChosen={isSemantic}
            onChosen={() => changeRetrievalMethod(RETRIEVE_METHOD.semantic)}
          />
        )}
      </div>

      {/* 参数设置 */}
      <RadioCard
        className={cn(style.radioItem, 'active mt-2')}
        title={t('dataset.retrieval.parameter_settings.title')}
        disabled={disabled}
        description={''}
        isChosen={true}
        noRadio={true}
        chosenConfig={
          <RetrievalParamConfigXiyan
            // disabled={disabled}
            ref={paramsRef}
            type={currentRetrievalMethod}
            value={value}
            onChange={onChange}
            showConfig={true}
            showTopKItem={true}
            showWeightedScore={false}
            layoutMethod="vertical"
          />
        }
      />
      {/* 引用和归属 */}
      {isInApp && (
        <Citation disabled={disabled} onChange={handleFeaturesChange} />
      )}
    </div>
  )
})

RetrievalMethodConfigXiyan.displayName = 'RetrievalMethodConfigXiyan'

export default memo(RetrievalMethodConfigXiyan)
